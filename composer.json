{"$schema": "https://getcomposer.org/schema.json", "name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.2", "ext-fileinfo": "*", "ext-imagick": "*", "bugsnag/bugsnag-laravel": "^2.0", "chillerlan/php-qrcode": "^5.0", "faisal50x/blade-ionicons": "^1.0", "filament/filament": "^3.2", "laravel-notification-channels/twilio": "^4.1", "laravel/framework": "^12.0", "laravel/nightwatch": "^1.7", "laravel/sanctum": "^4.0", "laravel/tinker": "^2.9", "league/flysystem-aws-s3-v3": "^3.0", "livewire/flux": "^2.0", "livewire/flux-pro": "^2.0", "livewire/livewire": "^3.5", "livewire/volt": "^1.6", "maennchen/zipstream-php": "^3.1", "mallardduck/blade-lucide-icons": "^1.23", "spatie/laravel-enum": "^3.1", "spatie/laravel-queueable-action": "^2.16.2", "symfony/http-client": "^7.1", "symfony/postmark-mailer": "^7.1", "toponepercent/baum": "^3.2", "wnx/sidecar-browsershot": "^2.3"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.13", "fakerphp/faker": "^1.23", "laravel/pail": "^1.2", "laravel/pint": "^1.13", "laravel/sail": "^1.26", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.0", "pestphp/pest": "^3.0", "pestphp/pest-plugin-laravel": "^3.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan filament:upgrade"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"], "dev": ["Composer\\Config::disableProcessTimeout", "npx concurrently -c \"#93c5fd,#c4b5fd,#fb7185,#fdba74\" \"php artisan serve\" \"php artisan queue:listen --tries=1\" \"php artisan pail --timeout=0\" \"npm run dev\" --names=server,queue,logs,vite"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true, "repositories": {"flux-pro": {"type": "composer", "url": "https://composer.fluxui.dev"}}}