<?php

namespace App\Livewire;

use App\Actions\ProcessExteriorVehicleImage;
use App\Models\ImageUpload;
use App\Models\Vehicle;
use App\Models\VehiclePhotoGuide;
use finfo;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Livewire\Attributes\Computed;
use Livewire\Component;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;
use Livewire\WithFileUploads;

class VehiclePhotos extends Component
{
    use WithFileUploads;

    public Vehicle $vehicle;

    public ?TemporaryUploadedFile $upload = null;

    #[Computed]
    public function photoGuides()
    {
        return VehiclePhotoGuide::query()
            ->orderBy('sort_order')
            ->get();
    }

    #[Computed]
    public function images()
    {
        return $this->vehicle
            ->images()
            ->with([
                'vehiclePhotoGuide',
                'finalImage',
            ])
            ->orderBy('sort_order')
            ->get();
        //            ->map(fn($binary) => [
        //                'id' => $binary->id,
        //                'url' => $binary->processedImage?->url() ?: $binary->url(),
        //                'vehicle_photo_guide_id' => $binary->vehicle_photo_guide_id,
        //            ]);
    }

    //    public function getImages()
    //    {
    //        return $this->images;
    //    }

    public function uploadImage($binary, $photoGuideId)
    {
        $binary = base64_decode(Str::after($binary, 'base64,'));
        $mimeType = (new finfo(FILEINFO_MIME_TYPE))->buffer($binary);
        $unique = Str::orderedUuid();
        $fileExtension = Str::after($mimeType, '/');
        $path = "vehicles/{$this->vehicle->uuid}/{$unique}.{$fileExtension}";

        Storage::put($path, $binary);

        if ($photoGuideId) {
            $this->vehicle->images()
                ->where('vehicle_photo_guide_id', $photoGuideId)
                ->delete();
        }

        $photoGuide = $this->photoGuides->find($photoGuideId);

        $imageUpload = $this->vehicle->images()->create([
            'path' => $path,
            'size' => strlen($binary),
            'type' => $mimeType,
            'vehicle_photo_guide_id' => $photoGuideId ?: null,
            'sort_order' => $photoGuide->sort_order ?? $this->vehicle->images()->count() + 1,
        ]);
    }

    public function uploadFile()
    {
        $this->validate([
            'upload' => 'required|image:allow_svg|max:5000', // 1MB Max
        ]);

        $unique = Str::orderedUuid();
        $fileExtension = $this->upload->getClientOriginalExtension();

        $path = $this->upload->storeAs(
            "vehicles/{$this->vehicle->uuid}",
            "{$unique}.{$fileExtension}"
        );

        $imageUpload = $this->vehicle->images()->create([
            'path' => $path,
            'size' => $this->upload->getSize(),
            'type' => $this->upload->getMimeType(),
            'sort_order' => $this->vehicle->images()->count() + 1,
        ]);
        //        $this->images[] = $imageUpload->url();
        //        unset($this->images);
    }

    public function removeBackground(ImageUpload $image)
    {
        $processExteriorVehicleImage = app(ProcessExteriorVehicleImage::class);

        $processExteriorVehicleImage->execute($image);
    }

    public function render()
    {
        return view('livewire.vehicle-photos');
    }
}
